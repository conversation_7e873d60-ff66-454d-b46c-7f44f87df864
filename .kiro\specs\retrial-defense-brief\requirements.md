# 再审答辩状生成需求文档

## 项目概述

为徐达诉王宝宝等农村建房施工合同纠纷案件生成一份详细的再审答辩状，用于回应王宝宝可能提出的再审申请。

## 需求

### 需求1：案件基本信息整理

**用户故事：** 作为代理律师，我需要系统整理案件的基本信息，以便在答辩状中准确引用案件事实。

#### 验收标准
1. 当整理案件信息时，系统应当提取一审、二审判决书中的关键事实
2. 当引用法条时，系统应当准确引用相关法律条文
3. 当描述争议焦点时，系统应当基于已有判决书内容进行总结

### 需求2：再审条件分析

**用户故事：** 作为代理律师，我需要分析对方再审申请是否符合法定条件，以便在答辩中进行有力反驳。

#### 验收标准
1. 当分析再审理由时，系统应当对照《民事诉讼法》相关条文
2. 当评估事实认定时，系统应当基于一审、二审已查明的事实
3. 当论证程序合法性时，系统应当引用庭审笔录等程序性证据

### 需求3：逐项反驳论证

**用户故事：** 作为代理律师，我需要对对方可能提出的每个再审理由进行逐项反驳，以维护已生效判决的权威性。

#### 验收标准
1. 当反驳材料问题时，系统应当引用专家意见和二审法院的认定
2. 当反驳合同主体争议时，系统应当引用一审、二审法院的一致认定
3. 当反驳工期争议时，系统应当基于已认定的客观事实进行论证
4. 当反驳惩罚性赔偿请求时，系统应当说明法律适用的准确性

### 需求4：程序性抗辩

**用户故事：** 作为代理律师，我需要从程序角度论证不应启动再审程序，以节约司法资源。

#### 验收标准
1. 当论证案件已履行完毕时，系统应当说明再审的必要性问题
2. 当强调既判力时，系统应当引用相关司法解释
3. 当论证争议已决时，系统应当说明一审二审已充分审理的事实

### 需求5：格式规范要求

**用户故事：** 作为代理律师，我需要答辩状符合法院的格式要求，以确保文书的专业性。

#### 验收标准
1. 当生成文书时，系统应当遵循标准的答辩状格式
2. 当引用法条时，系统应当使用准确的法条编号和条文内容
3. 当署名时，系统应当包含律师信息和日期
4. 当分段论述时，系统应当逻辑清晰、层次分明

## 技术约束

- 必须基于项目中现有的法律文档内容
- 生成的答辩状应当使用正式的法律文书语言
- 所有事实引用必须有据可查
- 法条引用必须准确无误

## 验收标准

- 答辩状内容完整，涵盖所有可能的再审理由
- 论证逻辑严密，法理依据充分
- 格式符合法院要求
- 语言表达专业、准确