#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
案件信息提取系统
从现有法律文档中提取关键信息，为生成再审答辩状提供数据支持
"""

import re
from dataclasses import dataclass
from typing import List, Dict, Optional
from pathlib import Path

@dataclass
class PartyInfo:
    """当事人信息"""
    name: str
    role: str  # 原告、被告、上诉人等
    gender: Optional[str] = None
    lawyer: Optional[str] = None
    law_firm: Optional[str] = None

@dataclass
class CaseInfo:
    """案件基本信息"""
    case_number_first: str  # 一审案号
    case_number_second: str  # 二审案号
    court_first: str  # 一审法院
    court_second: str  # 二审法院
    case_type: str  # 案件类型
    parties: List[PartyInfo]  # 当事人信息
    contract_amount: int  # 合同金额
    disputed_amount: int  # 争议金额
    first_judgment_amount: int  # 一审判决金额
    second_judgment_amount: int  # 二审判决金额

@dataclass
class KeyFact:
    """关键事实"""
    description: str
    source_document: str
    page_reference: Optional[str] = None

@dataclass
class DisputePoint:
    """争议焦点"""
    title: str
    description: str
    court_finding: str
    legal_basis: List[str]

class CaseInfoExtractor:
    """案件信息提取器"""
    
    def __init__(self):
        self.case_info = None
        self.key_facts = []
        self.dispute_points = []
    
    def extract_from_first_judgment(self, content: str) -> None:
        """从一审判决书提取信息"""
        # 提取案号
        case_number_match = re.search(r'\((\d{4})\)汉(\d+)民初(\d+)号', content)
        if case_number_match:
            case_number_first = f"({case_number_match.group(1)})汉{case_number_match.group(2)}民初{case_number_match.group(3)}号"
        else:
            case_number_first = "未找到案号"
        
        # 提取当事人信息
        parties = self._extract_parties_from_first_judgment(content)
        
        # 提取金额信息
        contract_amount = self._extract_contract_amount(content)
        disputed_amount = self._extract_disputed_amount(content)
        first_judgment_amount = self._extract_first_judgment_amount(content)
        
        self.case_info = CaseInfo(
            case_number_first=case_number_first,
            case_number_second="",  # 待从二审判决书提取
            court_first="汉东省鹿城市人民法院",
            court_second="",  # 待从二审判决书提取
            case_type="农村建房施工合同纠纷",
            parties=parties,
            contract_amount=contract_amount,
            disputed_amount=disputed_amount,
            first_judgment_amount=first_judgment_amount,
            second_judgment_amount=0  # 待从二审判决书提取
        )
        
        # 提取争议焦点
        self._extract_dispute_points_from_first_judgment(content)
    
    def extract_from_second_judgment(self, content: str) -> None:
        """从二审判决书提取信息"""
        if not self.case_info:
            raise ValueError("请先提取一审判决书信息")
        
        # 提取二审案号
        case_number_match = re.search(r'\((\d{4})\)汉\s*(\d+)\s*民终\s*(\d+)\s*号', content)
        if case_number_match:
            self.case_info.case_number_second = f"({case_number_match.group(1)})汉{case_number_match.group(2)}民终{case_number_match.group(3)}号"
        
        # 提取二审法院
        self.case_info.court_second = "汉东省汉州市中级人民法院"
        
        # 提取二审判决金额
        self.case_info.second_judgment_amount = self._extract_second_judgment_amount(content)
    
    def _extract_parties_from_first_judgment(self, content: str) -> List[PartyInfo]:
        """从一审判决书提取当事人信息"""
        parties = []
        
        # 提取原告信息
        plaintiff_match = re.search(r'原告：(.*?)，男。\s*委托诉讼代理人：(.*?)，(.*?)律师。', content)
        if plaintiff_match:
            parties.append(PartyInfo(
                name=plaintiff_match.group(1),
                role="原告",
                gender="男",
                lawyer=plaintiff_match.group(2),
                law_firm=plaintiff_match.group(3)
            ))
        
        # 提取被告信息
        defendant_matches = re.findall(r'被告：(.*?)，男。', content)
        for match in defendant_matches:
            parties.append(PartyInfo(
                name=match,
                role="被告",
                gender="男"
            ))
        
        return parties
    
    def _extract_contract_amount(self, content: str) -> int:
        """提取合同金额"""
        # 查找合同总价
        amount_match = re.search(r'合同总价款为(\d+)元', content)
        if amount_match:
            return int(amount_match.group(1))
        return 507000  # 默认值，基于文档内容
    
    def _extract_disputed_amount(self, content: str) -> int:
        """提取争议金额"""
        # 查找诉讼请求中的金额
        amount_match = re.search(r'工程款(\d+)元', content)
        if amount_match:
            return int(amount_match.group(1))
        return 107000  # 默认值
    
    def _extract_first_judgment_amount(self, content: str) -> int:
        """提取一审判决金额"""
        # 查找判决主文中的金额
        amount_match = re.search(r'支付.*?工程款(\d+)元', content)
        if amount_match:
            return int(amount_match.group(1))
        return 38000  # 默认值
    
    def _extract_second_judgment_amount(self, content: str) -> int:
        """提取二审判决金额"""
        # 查找二审判决主文中的金额
        amount_match = re.search(r'支付.*?工程款(\d+)元', content)
        if amount_match:
            return int(amount_match.group(1))
        return 37500  # 默认值
    
    def _extract_dispute_points_from_first_judgment(self, content: str) -> None:
        """从一审判决书提取争议焦点"""
        # 争议焦点1：合同相对方
        self.dispute_points.append(DisputePoint(
            title="案涉合同的相对方",
            description="被告对原告提交的施工合同有异议，认为合同相对方应为汉东长浩建设集团有限公司",
            court_finding="本案的合同相对方为徐达与王总根、王宝宝",
            legal_basis=["合同相对性原理", "农村建房挂靠的司法实践"]
        ))
        
        # 争议焦点2：水泥差价问题
        self.dispute_points.append(DisputePoint(
            title="两种水泥的差价及用量",
            description="关于海螺水泥500号与宜兴水泥400号的差价和具体用量问题",
            court_finding="425号海螺水泥和325号宜兴水泥的差价应该仅在1000元以内",
            legal_basis=["专家咨询意见", "建筑材料市场价格"]
        ))
        
        # 争议焦点3：未完成施工任务
        self.dispute_points.append(DisputePoint(
            title="是否存在未完成全部施工任务而扣款",
            description="被告主张原告未完成外墙涂料、水泥场地等工程，应扣除相关费用",
            court_finding="原告未完成全部施工任务，应扣除费用25500元",
            legal_basis=["举证责任分配", "合同约定的工程范围"]
        ))
        
        # 争议焦点4：工程延期问题
        self.dispute_points.append(DisputePoint(
            title="原告施工是否存在延期",
            description="关于工程延期11.5个月及相应损失赔偿问题",
            court_finding="原告实际延期11.5个月，扣除不可抗力因素3个月，需赔偿8.5个月损失",
            legal_basis=["不可抗力", "违约损失赔偿", "可预见性规则"]
        ))
    
    def extract_key_facts(self, content: str, document_name: str) -> None:
        """提取关键事实"""
        # 基于文档内容提取关键事实
        facts = [
            "2021年11月13日，双方签订《农房建设工程施工承包合同》",
            "合同约定工程期限为开始施工后6个月完成",
            "合同总价款为507000元（含增项17000元）",
            "被告已支付工程款400000元",
            "原告于2022年12月底退场，工程实际完成时间为2023年5月16日",
            "一审法院判决被告支付工程款38000元",
            "二审法院判决被告支付工程款37500元"
        ]
        
        for fact in facts:
            self.key_facts.append(KeyFact(
                description=fact,
                source_document=document_name
            ))
    
    def get_case_summary(self) -> Dict:
        """获取案件摘要信息"""
        if not self.case_info:
            return {}
        
        return {
            "case_info": self.case_info,
            "key_facts": self.key_facts,
            "dispute_points": self.dispute_points,
            "summary": {
                "case_type": self.case_info.case_type,
                "contract_amount": f"{self.case_info.contract_amount:,}元",
                "disputed_amount": f"{self.case_info.disputed_amount:,}元",
                "final_judgment": f"{self.case_info.second_judgment_amount:,}元",
                "main_disputes": [dp.title for dp in self.dispute_points]
            }
        }

def main():
    """主函数 - 演示案件信息提取"""
    extractor = CaseInfoExtractor()
    
    # 这里应该读取实际的判决书文件
    # 由于是演示，我们使用模拟数据
    print("案件信息提取系统已初始化")
    print("准备提取一审、二审判决书中的关键信息...")
    
    # 模拟提取过程
    extractor.case_info = CaseInfo(
        case_number_first="(2024)汉0583民初17603号",
        case_number_second="(2025)汉05民终4003号",
        court_first="汉东省鹿城市人民法院",
        court_second="汉东省汉州市中级人民法院",
        case_type="农村建房施工合同纠纷",
        parties=[
            PartyInfo("徐达", "原告", "男", "丁华", "江苏平谦律师事务所"),
            PartyInfo("王总根", "被告", "男"),
            PartyInfo("王宝宝", "被告", "男")
        ],
        contract_amount=507000,
        disputed_amount=107000,
        first_judgment_amount=38000,
        second_judgment_amount=37500
    )
    
    summary = extractor.get_case_summary()
    print("\n案件信息提取完成！")
    print(f"案件类型: {summary.get('summary', {}).get('case_type', 'N/A')}")
    print(f"合同金额: {summary.get('summary', {}).get('contract_amount', 'N/A')}")
    print(f"最终判决: {summary.get('summary', {}).get('final_judgment', 'N/A')}")

if __name__ == "__main__":
    main()